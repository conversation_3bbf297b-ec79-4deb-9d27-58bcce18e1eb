<template>
    <div class="menu-reset-wrapper">
        <el-tree ref="elTree" :data="processedMenuData" :props="treeProps" :default-expanded-keys="defaultExpandedKeys"
            :current-node-key="currentNodeKey" :filter-node-method="filterNode" @node-click="handleNodeClick"
            @node-expand="handleNodeExpand" @node-collapse="handleNodeCollapse" node-key="treeId" highlight-current
            :expand-on-click-node="true">
            <template #default="{ node, data }">
                <div class="tree-node-content">
                    <!-- 图标显示 -->
                    <template v-if="data.iconType === 'image' && data.icon">
                        <img :src="data.icon" class="custom-icon" alt="menu icon" />
                    </template>
                    <template v-else-if="data.icon">
                        <i :class="['custom-icon', data.icon]"></i>
                    </template>

                    <!-- 节点文本 -->
                    <span class="node-label">{{ data.treeName }}</span>
                </div>
            </template>
        </el-tree>
    </div>
</template>

<script>
export default {
    name: 'NavTree',

    props: {
        // 菜单数据，与原组件保持一致的结构
        menuData: {
            type: Array,
            required: true,
            default: () => []
        },
        // 过滤文本，用于搜索菜单
        filterText: {
            type: String,
            default: ''
        },
        // 当前激活的菜单项ID
        activeItemId: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            // 树组件的配置项
            treeProps: {
                children: 'children',
                label: 'treeName'
            },
            // 当前选中的节点ID
            currentNodeKey: '',
            // 本地过滤文本
            localFilterText: ''
        };
    },

    watch: {
        // 监听激活项ID变化，同步更新选中状态
        activeItemId(newVal) {
            if (newVal) {
                this.setActiveNode(newVal);
            } else {
                this.currentNodeKey = '';
            }
        },

        // 监听过滤文本变化，触发树过滤
        filterText(newVal) {
            this.localFilterText = newVal.toLowerCase();
            this.$refs.elTree.filter(newVal);
            this.$nextTick(() => {
                this.expandFilteredNodes();
            });
        },

        // 当选中节点变化时，通知父组件
        currentNodeKey(newVal) {
            this.$emit('update:activeItemId', newVal);
        }
    },

    computed: {
        // 处理后的菜单数据（深拷贝避免修改原数据）
        processedMenuData() {
            return JSON.parse(JSON.stringify(this.menuData));
        },

        // 默认展开的节点ID集合
        defaultExpandedKeys() {
            return this.collectExpandedKeys(this.processedMenuData);
        }
    },

    methods: {
        // 节点过滤方法
        filterNode(value, data) {
            if (!value) return true;
            return data.treeName.toLowerCase().includes(value.toLowerCase());
        },

        // 收集需要默认展开的节点ID
        collectExpandedKeys(items) {
            const keys = [];
            items.forEach(item => {
                // 有子节点的节点需要展开
                if (item.children && item.children.length) {
                    keys.push(item.treeId);
                    keys.push(...this.collectExpandedKeys(item.children));
                }
            });
            return keys;
        },

        // 展开所有包含过滤结果的节点
        expandFilteredNodes() {
            if (!this.localFilterText) return;

            const expandNodes = (nodes) => {
                nodes.forEach(node => {
                    if (node.children && node.children.length) {
                        // 检查是否有子节点匹配过滤条件
                        const hasMatchingChild = node.children.some(child =>
                            child.treeName.toLowerCase().includes(this.localFilterText) ||
                            (child.children && this.hasMatchingDescendant(child))
                        );

                        if (hasMatchingChild || node.treeName.toLowerCase().includes(this.localFilterText)) {
                            expandNodes(node.children);
                        }
                    }
                });
            };

            expandNodes(this.processedMenuData);
        },

        // 检查是否有后代节点匹配过滤条件
        hasMatchingDescendant(node) {
            if (node.treeName.toLowerCase().includes(this.localFilterText)) return true;
            if (node.children && node.children.length) {
                return node.children.some(child => this.hasMatchingDescendant(child));
            }
            return false;
        },

        // 处理节点点击事件
        handleNodeClick(data, node) {
            // 只有三级节点可以被激活
            if (data.level === 3) {
                this.currentNodeKey = data.treeId;
                this.$emit('item-click', data);
            } 
            // else if (data.children && data.children.length) {
            //     // 切换非叶子节点的展开状态
            //     this.$refs.elTree.toggleNodeExpansion(node);
            // }
        },

        // 设置激活节点并展开所有父节点
        setActiveNode(nodeId) {
            if (!nodeId) return;

            // 找到节点并设置为当前节点
            const node = this.findNodeById(this.processedMenuData, nodeId);
            if (node && node.level === 3) {
                this.currentNodeKey = nodeId;
                // 展开所有父节点
                this.expandParentNodes(nodeId);
            }
        },

        // 递归查找节点
        findNodeById(nodes, id) {
            for (const node of nodes) {
                if (node.treeId === id) {
                    return node;
                }
                if (node.children && node.children.length) {
                    const found = this.findNodeById(node.children, id);
                    if (found) return found;
                }
            }
            return null;
        },

        // 展开所有父节点
        expandParentNodes(nodeId) {
            const node = this.$refs.elTree.getNode(nodeId);
            if (node && node.parent) {
                let parent = node.parent;
                while (parent && parent.level > 0) {
                    parent = parent.parent;
                }
            }
        },

        // 处理节点展开事件
        handleNodeExpand(data) {
            this.$emit('submenu-open', data.treeId);
        },

        // 处理节点折叠事件
        handleNodeCollapse(data) {
            this.$emit('submenu-close', data.treeId);
        }
    },

    mounted() {
        // 初始化时设置激活节点
        if (this.activeItemId) {
            this.setActiveNode(this.activeItemId);
        }
    }
};
</script>

<style lang="scss">
.menu-reset-wrapper {
    padding: 10px 0;

    /* 节点内容样式 */
    .el-tree-node__content {
        display: flex;
        align-items: center;
        height: 40px !important;
        line-height: 40px !important;
        padding: 0 10px;
        margin: 2px 0;
        border-radius: 5px;
        font-size: 14px !important;
        color: #1D2129;
        .el-tree-node__expand-icon{
            position: absolute;
            right: 20px;
        }
    }

    /* 图标样式 */
    .custom-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        font-size: 16px !important;
    }
}
</style>
