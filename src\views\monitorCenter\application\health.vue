<template>
  <div class="wrap flex-b">
    <div class="aqItem flex-b" v-for="(item, i) in list" :key="i">
      <img :src="item.icon" class="icon" />
      <div class="info">
        <div class="name">{{ item.name }}</div>
        <div class="flex-b">
          <div class="num" v-if="item.name == '待办工单'">
            最近7天：
            <span class="blue">
              {{ item.num == 0 ? 0 : item.num ? item.num : "-" }}
            </span>
            个
          </div>
          <div v-if="item.name == '断点监测'">
            {{ item.status ? "正常" : "异常" }}
          </div>
          <div v-else>
            待处理：
            <span class="blue">
              {{ item.num == 0 ? 0 : item.num ? item.num : "-" }}
            </span>
            个
          </div>
          <div class="history" @click="handleViewClick(item)">查看</div>
        </div>
      </div>
    </div>

    <!-- 断点监测弹窗 -->
    <el-dialog
      title="断点监测"
      :visible.sync="breakpointDialogVisible"
      width="900px"
      :before-close="handleCloseBreakpointDialog"
    >
      <el-table
        :data="breakpointMonitorData"
        border
        v-loading="breakpointLoading"
        element-loading-text="加载中..."
      >
        <el-table-column prop="monitorIp" label="监测IP" align="center" />
        <el-table-column prop="monitorTime" label="监测时间" align="center" />
        <el-table-column prop="ipStatus" label="IP状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.ipStatus)">
              {{ getStatusText(scope.row.ipStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: center">
        <el-pagination
          @current-change="handleBreakpointPageChange"
          @size-change="handleBreakpointSizeChange"
          :current-page="breakpointQueryParams.pageNum"
          :page-size="breakpointQueryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="breakpointTotal"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="breakpointDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 基础设施告警弹框 -->
    <el-dialog
      title="基础设施告警"
      :visible.sync="warningDialogVisible"
      width="100%"
      top="0"
    >
      <div style="width: 100%; height: calc(100vh - 180px)">
        <iframe
          :src="warningSrc"
          frameborder="no"
          style="width: 100%; height: 100%"
          scrolling="auto"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getApplication } from "@/api/property/applicationManage";
import { listBreakpointMonitor } from "@/api/monitor/breakpointMonitor";
import { getYdjcymtz } from "@/api/login";
export default {
  data() {
    return {
      projectName: "",
      yyId: "",
      list: [
        {
          name: "待办工单",
          num: 0,
          icon: require("@/assets/images/monitor/dbgd.png"),
          path: "/serve/workorder",
        },
        // {
        //   name: "通报告警",
        //   num: 0,
        //   icon: require("@/assets/images/monitor/tgbj.png"),
        //   path: "/warning/warningList"
        // },
        {
          name: "断点监测",
          status: true,
          icon: require("@/assets/images/monitor/ddjc.png"),
          path: "",
        },
      ],
      // 断点监测弹窗相关数据
      breakpointDialogVisible: false,
      breakpointMonitorData: [],
      breakpointLoading: false,
      breakpointTotal: 0,
      breakpointQueryParams: {
        pageNum: 1,
        pageSize: 10,
        dataId: null, // 应用ID，用于查询该应用的断点监测数据
        type: 1,
      },
      //基础设施告警弹框
      warningDialogVisible: false,
      warningSrc: "",
    };
  },
  mounted() {
    this.loadApplicationDetail();
  },
  methods: {
    async loadApplicationDetail() {
      const applicationId = this.$route.params.id || this.$route.query.id;

      if (!applicationId) {
        this.$message.error("应用ID不能为空");
        return;
      }
      const response = await getApplication(applicationId);

      if (response.code === 200 && response.data) {
        console.log(response.data);
        this.projectName = response.data.name;
        this.yyId = response.data.id;
        this.list = [
          {
            name: "待办工单",
            num: response.data.dbgd,
            icon: require("@/assets/images/monitor/dbgd.png"),
            path: "/serve/workorder",
          },
          {
            name: "基础设施告警",
            num: 0,
            icon: require("@/assets/images/monitor/tgbj.png"),
            path: "",
          },
          {
            name: "断点监测",
            status: response.data.status == "1" ? true : false,
            icon: require("@/assets/images/monitor/ddjc.png"),
            path: "",
          },
        ];
      } else {
        console.error("获取应用详情失败:", response.msg);
        this.$message.error(response.msg || "获取应用详情失败");
      }
    },

    // 处理查看按钮点击
    handleViewClick(item) {
      if (item.name === "断点监测") {
        this.showBreakpointDialog();
      } else if (item.name === "基础设施告警") {
        getYdjcymtz({ projectName: this.projectName }).then((res) => {
          if (res.code == 200) {
            this.warningSrc = res.msg;
            this.showWarningDialog();
          }
        });
      } else if (item.path) {
        this.jump(item.path, { yyId: this.yyId });
      }
    },

    // 跳转到其他页面
    jump(path, query) {
      this.$router.push({
        path: path,
        query: query,
      });
    },

    // 显示断点监测弹窗
    showBreakpointDialog() {
      this.loadBreakpointMonitorData();
      this.breakpointDialogVisible = true;
    },

    // 显示基础设施告警弹窗
    showWarningDialog() {
      this.warningDialogVisible = true;
    },

    // 加载断点监测数据
    async loadBreakpointMonitorData() {
      try {
        this.breakpointLoading = true;

        // 设置应用ID查询参数
        const applicationId = this.$route.params.id || this.$route.query.id;
        this.breakpointQueryParams.dataId = applicationId;

        console.log("查询断点监测数据，参数:", this.breakpointQueryParams);

        const response = await listBreakpointMonitor(
          this.breakpointQueryParams
        );
        console.log("断点监测API响应:", response);

        if (response.code === 200 && response.data) {
          // 处理返回的数据
          this.breakpointMonitorData = (response.data.list || []).map(
            (item) => ({
              ...item,
              // 格式化监测时间
              monitorTime: item.cTime,
              // 格式化监测IP
              monitorIp: item.monitorIp || item.jcIp || item.ip,
              // 格式化IP状态
              ipStatus: item.ipStatus || item.status,
              // 响应时间
              responseTime: item.responseTime || item.xyTime,
              // 错误信息
              errorMsg: item.errorMsg || item.errorMessage || item.cwxx,
            })
          );

          this.breakpointTotal = response.data.total || 0;
          console.log("断点监测数据加载完成:", this.breakpointMonitorData);
        } else {
          console.error("获取断点监测数据失败:", response.msg);
          this.breakpointMonitorData = [];
          this.breakpointTotal = 0;
          this.$message.error(response.msg || "获取断点监测数据失败");
        }
      } catch (error) {
        console.error("加载断点监测数据异常:", error);
        this.breakpointMonitorData = [];
        this.breakpointTotal = 0;
        this.$message.error("加载断点监测数据失败");
      } finally {
        this.breakpointLoading = false;
      }
    },

    // 关闭断点监测弹窗
    handleCloseBreakpointDialog() {
      this.breakpointDialogVisible = false;
      this.breakpointMonitorData = [];
      this.breakpointTotal = 0;
      // 重置分页参数
      this.breakpointQueryParams.pageNum = 1;
      this.breakpointQueryParams.pageSize = 10;
    },

    // 断点监测分页 - 当前页变化
    handleBreakpointPageChange(page) {
      this.breakpointQueryParams.pageNum = page;
      this.loadBreakpointMonitorData();
    },

    // 断点监测分页 - 每页条数变化
    handleBreakpointSizeChange(size) {
      this.breakpointQueryParams.pageSize = size;
      this.breakpointQueryParams.pageNum = 1;
      this.loadBreakpointMonitorData();
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return "-";
      if (typeof time === "string") return time;
      // 如果是时间戳，转换为日期字符串
      if (typeof time === "number") {
        return new Date(time).toLocaleString("zh-CN");
      }
      return time.toString();
    },

    // 获取状态类型（用于el-tag的type属性）
    getStatusType(status) {
      if (!status) return "info";
      const statusStr = status.toString().toLowerCase();
      if (statusStr === "1" || statusStr === "正常" || statusStr === "normal") {
        return "success";
      } else if (
        statusStr === "0" ||
        statusStr === "异常" ||
        statusStr === "error"
      ) {
        return "danger";
      }
      return "info";
    },

    // 获取状态文本
    getStatusText(status) {
      if (!status) return "未知";
      const statusStr = status.toString().toLowerCase();
      if (statusStr === "1" || statusStr === "正常" || statusStr === "normal") {
        return "正常";
      } else if (
        statusStr === "2" ||
        statusStr === "异常" ||
        statusStr === "error"
      ) {
        return "异常";
      }
      return status.toString();
    },
  },
};
</script>

<style lang="scss" scoped>
.aqItem {
  padding: 12px;
  box-sizing: border-box;
  flex: 1;
  margin-right: 40px;
  .icon {
    width: 82px;
    height: 82px;
    margin-right: 16px;
  }
  .info {
    width: 100%;
    height: 72px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .name {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 18px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
    }
    .num {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #4e5969;
      line-height: 24px;
      text-align: left;
      .blue {
        color: #0057fe;
      }
    }
    .history {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #667085;
      line-height: 24px;
      text-align: left;
      cursor: pointer;
    }
  }
}
</style>
