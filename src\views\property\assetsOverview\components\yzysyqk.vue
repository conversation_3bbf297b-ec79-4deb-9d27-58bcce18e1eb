<template>
  <div>
    <div class="tabList flex-c">
      <div
        class="tab"
        v-for="(item, i) in tabList"
        :key="i"
        :class="tabIndex == i ? 'tab_active' : ''"
        @click="changeTab(i)"
      >
        {{ item }}
      </div>
    </div>
    <el-table :data="datalist" style="margin-top: 12px">
      <el-table-column prop="ph" label="排行" align="center" />
      <el-table-column prop="bmmc" label="部门名称" align="center" />
      <el-table-column prop="sl" label="数量" align="center" />
    </el-table>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabList: ["虚拟机", "云数据库", "负载均衡", "对象存储"],
      tabIndex: 0,
      datalist: [
        { ph: "1", bmmc: "金华市数据局", sl: "32" },
        { ph: "2", bmmc: "东阳市数据局", sl: "32" },
        { ph: "3", bmmc: "永康市数据局", sl: "32" },
      ],
    };
  },
  methods: {
    changeTab(i) {
      this.tabIndex = i;
    },
  },
};
</script>

<style lang="scss" scoped>
.tabList {
  .tab {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #86909c;
    line-height: 22px;
    text-align: left;
    margin-right: 46px;
    cursor: pointer;
  }
  .tab_active {
    color: #0057fe;
  }
}
</style>
