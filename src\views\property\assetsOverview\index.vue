<template>
  <div class="container">
    <div class="card">
      <div class="cardTitle">资产总览</div>
      <zczl :list="zczlData"></zczl>
    </div>
    <div class="flex-s">
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">云资源使用量TOP5</div>
        <yzysyqk></yzysyqk>
      </div>
      <div class="card" style="width: 67%">
        <div class="cardTitle">云资源使用情况统计</div>
        <top5 :data1="top5PieData" :data2="top5LineData"></top5>
      </div>
    </div>
    <div class="flex-s">
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">系统等保级别统计</div>
        <dbjb :data="dbjbData"></dbjb>
      </div>
      <div class="card" style="width: 33%; margin-right: 12px">
        <div class="cardTitle">资产标签分布</div>
        <zcbq :data="zcbqData"></zcbq>
      </div>
      <div class="card" style="width: 33%">
        <div class="cardTitle">资产数量变化趋势</div>
        <zcsl :data="zcslData"></zcsl>
      </div>
    </div>
  </div>
</template>

<script>
import zczl from "@/views/property/assetsOverview/components/zczl.vue";
import yzysyqk from "@/views/property/assetsOverview/components/yzysyqk.vue";
import top5 from "@/views/property/assetsOverview/components/top5.vue";
import dbjb from "@/views/property/assetsOverview/components/dbjb.vue";
import zcbq from "@/views/property/assetsOverview/components/zcbq.vue";
import zcsl from "@/views/property/assetsOverview/components/zcsl.vue";
export default {
  components: { zczl, yzysyqk, dbjb, top5, zcbq, zcsl },
  data() {
    return {
      zczlData: [
        {
          name: "资产总数",
          num: 1798,
          unit: "家",
          icon: require("@/assets/images/property/dwzs.png"),
          list: [{ label: "风险单位", value: "0家" }],
        },
        {
          name: "系统总数",
          num: 14081,
          unit: "个",
          icon: require("@/assets/images/property/xtzs.png"),
          list: [
            { label: "缺陷系统", value: "0个" },
            { label: "高风险系统", value: "0个" },
            { label: "低风险系统", value: "0个" },
          ],
        },
        {
          name: "云资源总数",
          num: 10086,
          unit: "台",
          icon: require("@/assets/images/property/yzyzs.png"),
          list: [
            { label: "ECS", value: "0台" },
            { label: "RDS", value: "0台" },
            { label: "SLB", value: "0台" },
            { label: "OSS", value: "0台" },
          ],
        },
        {
          name: "IP资产总数",
          num: 33308,
          unit: "个",
          icon: require("@/assets/images/property/ipzczs.png"),
          list: [
            { label: "风险单位", value: "0个" },
            { label: "风险单位", value: "0个" },
            { label: "风险单位", value: "0个" },
          ],
        },
      ],
      dbjbData: [
        { name: "等保五级", value: 20 },
        { name: "等保四级", value: 40 },
        { name: "等保三级", value: 50 },
        { name: "等保二级", value: 80 },
        { name: "等保一级", value: 110 },
      ],
      top5PieData: [
        { name: "极低负载", value: 446 },
        { name: "低负载", value: 665 },
        { name: "正常负载", value: 1480 },
        { name: "高负载", value: 426 },
        { name: "极高负载", value: 114 },
      ],
      top5LineData: [
        { name: "5.18", value1: "4", value2: "30" },
        { name: "5.20", value1: "6", value2: "35" },
        { name: "5.22", value1: "14", value2: "20" },
        { name: "5.24", value1: "4", value2: "40" },
        { name: "5.26", value1: "11", value2: "21" },
        { name: "5.28", value1: "1", value2: "22" },
      ],
      zcslData: [
        { name: "5.18", value1: "4", value2: "30" },
        { name: "5.20", value1: "6", value2: "35" },
        { name: "5.22", value1: "14", value2: "20" },
        { name: "5.24", value1: "4", value2: "40" },
        { name: "5.26", value1: "11", value2: "21" },
        { name: "5.28", value1: "1", value2: "22" },
      ],
      zcbqData: [
        { name: "政务云", value: 12 },
        { name: "政务外网", value: 16 },
        { name: "互联网", value: 11 },
        { name: "阿里云云盾", value: 13 },
        { name: "服务器", value: 18 },
        { name: "网关", value: 30 },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
}
.flex-s {
  display: flex;
  align-items: stretch;
}
</style>
