<template>
  <div class="tabWrap">
    <div class="flex-c" @click="goBack" style="cursor: pointer">
      <i class="el-icon-arrow-left icon"></i>
      <div class="flex-c" v-if="tabIndex == 0">
        <div class="tabName">{{ systemName }}</div>
        <div class="tag flex-c-c" v-for="(x, i) in tag" :key="i">{{ x }}</div>
      </div>
      <div class="tabName" v-else>
        {{ tablist[tabIndex].name }}
      </div>
    </div>
    <div class="tablist flex-c">
      <div
        class="tab"
        v-for="(item, i) in tablist"
        :key="i"
        :class="tabIndex == i ? 'tab_active' : ''"
      >
        <!-- <span>{{item.name}}</span> -->
        <a href="javascript:;" @click.prevent="changeTab(i, item)">
          {{ item.name }}
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabIndex: {
      type: Number,
      default: 0,
    },
    tablist: {
      type: Array,
      default: () => [],
    },
    systemName: {
      type: String,
      default: "",
    },
    tag: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // tablist: [
      //   { name: "值班看板" },
      //   { name: "排班管理" },
      //   { name: "交接日志" },
      //   { name: "统计分析" },
      // ],
    };
  },
  methods: {
    changeTab(i, item) {
      item.index = i;
      this.$emit("changeTab", item);
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.tabWrap {
  width: 100%;
  height: 107px;
  background-image: url("~@/assets/images/childrenTabBg.png");
  background-size: 100% 100%;
  padding: 20px 0 0 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .icon {
    color: #fff;
    font-weight: 700;
  }
  .tabName {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #ffffff;
    line-height: 24px;
    text-align: left;
    margin-left: 8px;
    margin-right: 12px;
  }
  .tag {
    padding: 4px 10px;
    box-sizing: border-box;
    margin-right: 8px;
    background-color: #eff6ff;
    border-radius: 4px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: #2563eb;
    line-height: 16px;
    text-align: left;
  }
}
.tablist {
  margin-left: 40px;
  .tab {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-size: 14px;
    color: #ffffff;
    line-height: 22px;
    text-align: left;
    cursor: pointer;
    margin-right: 50px;
    padding-bottom: 10px;
  }
  .tab_active {
    position: relative;
    font-weight: 700;
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 56px;
      height: 2px;
      background-color: #fff;
    }
  }
}
</style>
