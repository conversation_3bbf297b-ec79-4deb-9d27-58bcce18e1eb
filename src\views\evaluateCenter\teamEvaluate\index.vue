<template>
  <div>
    <div class="evaluate-box flex">
      <div class="evaluate-list evaluate-cycle flex-1">
        <div class="title">考核周期</div>
        <div class="content">{{ headerStatistics.period }}</div>
      </div>
      <div class="evaluate-list evaluate-object flex-1">
        <div class="title">考核对象</div>
        <div class="content">{{ headerStatistics.assessmentCount }}</div>
      </div>
      <div class="evaluate-list evaluate-number flex-1">
        <div class="title">待考核数量</div>
        <div class="content">{{ headerStatistics.underAssessmentCount }}</div>
      </div>
    </div>
    <div class="app-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
        <el-form-item label="考评状态" prop="isEvaluate">
          <el-select
            v-model="queryParams.isEvaluate"
            placeholder="请选择"
            style="width: 180px"
          >
            <el-option label="待考核" value="2"></el-option>
            <el-option label="已考核" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="考评团队" prop="evaluateApplication">
          <el-input v-model="queryParams.evaluateApplication" placeholder="请输入" style="width: 180px"></el-input>
        </el-form-item>
        <el-form-item label="考评时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="tableData">
<!--        <el-table-column type="selection" width="55" align="center" />-->
        <el-table-column label="序号" type="index" width="55" align="center" />
        <el-table-column label="考评周期" prop="evaluateCycle" width="100" />
        <el-table-column label="考评对象" prop="evaluateApplication" :show-overflow-tooltip="true" />
        <el-table-column label="运维应用" prop="appNames" :show-overflow-tooltip="true" />
        <el-table-column label="考评时间" prop="evaluateTime" width="150" v-if="showColumn"/>
        <el-table-column label="考评人" prop="evaluator" width="120" v-if="showColumn"/>
        <el-table-column label="考评分数" prop="overallScore" width="100" v-if="showColumn"/>
        <el-table-column label="评语" prop="evaluateContent" :show-overflow-tooltip="true" width="200" v-if="showColumn"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEvaluate(scope.row)"
              v-hasPermi="['system:role:edit']"
            >考评</el-button>
            <el-button
              v-if="scope.row.overallScore"
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleCheck(scope.row)"
              v-hasPermi="['system:role:edit']"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 评分编辑和查看 -->
      <el-dialog :custom-class="'evaluate-dialog-wrapper'" :show-close="false" :visible.sync="open" width="620px" append-to-body @close="cancel">
        <div class="evaluate-dialog">
          <div class="evaluate-dialog-header flex">
            <div class="evaluate-dialog-title">运维团队</div>
            <div class="evaluate-dialog-subtitle">考评报告</div>
            <i class="icon-close" @click="cancel"></i>
          </div>
          <div class="evaluate-dialog-form">
            <div class="evaluate-dialog-chart-title">运维团队：{{ evaluateFrom.belongingTeam }}</div>
            <div class="form-box">
              <el-form :inline="true" ref="evaluateFrom" :model="evaluateFrom" :rules="rules" class="demo-form-inline" label-position="top">
                <el-form-item label="综合得分" prop="overallScore">
                  <el-input v-model="evaluateFrom.overallScore" :readonly="dialogType === 'check'" placeholder="综合得分" style="width: 180px"></el-input>
                </el-form-item>
                <el-form-item label="问题影响时长评分" prop="problemEffectScore">
                  <el-input v-model="evaluateFrom.problemEffectScore" :readonly="dialogType === 'check'" placeholder="问题影响时长评分" style="width: 180px"></el-input>
                </el-form-item>
                <el-form-item label="技术能力评分" prop="technicalAbilityScore">
                  <el-input v-model="evaluateFrom.technicalAbilityScore" :readonly="dialogType === 'check'" placeholder="技术能力评分" style="width: 180px"></el-input>
                </el-form-item>
                <el-form-item label="操作合规性评分" prop="operateComplianceScore">
                  <el-input v-model="evaluateFrom.operateComplianceScore" :readonly="dialogType === 'check'" placeholder="操作合规性评分" style="width: 180px"></el-input>
                </el-form-item>
                <el-form-item label="协调性评分" prop="coordinationScore">
                  <el-input v-model="evaluateFrom.coordinationScore" :readonly="dialogType === 'check'" placeholder="协调性评分" style="width: 180px"></el-input>
                </el-form-item>
                <el-form-item label="评语" prop="evaluateContent" class="evaluateContent">
                  <el-input type="textarea" v-model="evaluateFrom.evaluateContent" :readonly="dialogType === 'check'" :rows="2" resize="none" placeholder="请输入" style="width: 100%"></el-input>
                </el-form-item>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="cancel" >关 闭</el-button>
                <el-button type="primary" @click="submitForm" v-if="dialogType !== 'check'">提 交</el-button>
              </div>
            </div>
          </div>
          <div class="evaluate-dialog-chart">
            <div class="evaluate-dialog-chart-title">考评报告</div>
            <div class="evaluate-dialog-chart-overview flex">
              <div class="evaluate-dialog-chart-overview-list evaluate-dialog-chart-overview-app-number">
                <div class="evaluate-dialog-chart-overview-list-title">负责应用数</div>
                <div class="evaluate-dialog-chart-overview-list-content">2<span>个</span></div>
<!--                <div class="evaluate-dialog-chart-overview-list-content">{{quantityStatistics.applicationCount }}<span>个</span></div>-->
              </div>
              <div class="evaluate-dialog-chart-overview-list evaluate-dialog-chart-overview-rate">
                <div class="evaluate-dialog-chart-overview-list-title">工单差评率</div>
                <div class="evaluate-dialog-chart-overview-list-content">{{quantityStatistics.unsatisfactionRate}}<span>%</span></div>
              </div>
              <div class="evaluate-dialog-chart-overview-list evaluate-dialog-chart-overview-report-number">
                <div class="evaluate-dialog-chart-overview-list-title">应用被通报次数</div>
                <div class="evaluate-dialog-chart-overview-list-content">{{ quantityStatistics.notificationCount }}<span>次</span></div>
              </div>
              <div class="evaluate-dialog-chart-overview-list evaluate-dialog-chart-overview-overtime-number">
                <div class="evaluate-dialog-chart-overview-list-title">预警超时处置数</div>
                <div class="evaluate-dialog-chart-overview-list-content">{{ quantityStatistics.timeoutCount }}<span>个</span></div>
              </div>
            </div>
          </div>
          <div class="chart-box chart1">
            <line-chart :key="time" :chartData="dailyStatistics" :close="open"></line-chart>
          </div>
          <div class="chart-box chart2">
            <pie-chart :key="time" :chartData="gdTypeStatistics"></pie-chart>
          </div>
          <div class="chart-box chart3">
            <scatter-chart :key="time" :title="'应用故障次数'" :color="'#FF7D00'" :chartData="malfunctionStatistics"></scatter-chart>
          </div>
          <div class="chart-box chart4">
            <scatter-chart :key="time" :title="'预警超时处置数'" :color="'#FF1A00'" :chartData="overtimeStatistics"></scatter-chart>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {evaluateList, evaluateEdit, detailStatistics, headerStatistics} from "@/api/evaluate/api"
import LineChart from "@/views/evaluateCenter/components/lineChart.vue";
import PieChart from "@/views/evaluateCenter/components/pieChart.vue";
import scatterChart from "@/views/evaluateCenter/components/scatterChart.vue";

export default {
  name: "CenterEvaluate",
  components: {LineChart, PieChart, scatterChart},
  data() {
    return {
      time: new Date().getTime(),
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      tableData: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        belongingTeam: "",
        evaluateType: "1",
        isEvaluate: "2",
        beginTime: "",
        endTime: ""
      },
      // 表单参数
      evaluateFrom: {
        overallScore: "",
        problemEffectScore: "",
        technicalAbilityScore: "",
        operateComplianceScore: "",
        coordinationScore: "",
        evaluateContent: "",
      },
      // 表单校验
      rules: {
        overallScore: [
          { required: true, message: "综合评分不能为空", trigger: ["blur", "change"] },
          { pattern: /^(?:0|[1-9][0-9]?|100)$/ , message: '分值必须在0-100之间', trigger: ["blur", "change"],}
        ],
        problemEffectScore: [
          { required: true, message: "综合评分不能为空", trigger: ["blur", "change"] },
          { pattern: /^(?:0|[1-9][0-9]?|100)$/ , message: '分值必须在0-100之间', trigger: ["blur", "change"],}
        ],
        technicalAbilityScore: [
          { required: true, message: "综合评分不能为空", trigger: ["blur", "change"] },
          { pattern: /^(?:0|[1-9][0-9]?|100)$/ , message: '分值必须在0-100之间', trigger: ["blur", "change"],}
        ],
        operateComplianceScore: [
          { required: true, message: "综合评分不能为空", trigger: ["blur", "change"] },
          { pattern: /^(?:0|[1-9][0-9]?|100)$/ , message: '分值必须在0-100之间', trigger: ["blur", "change"],}
        ],
        coordinationScore: [
          { required: true, message: "综合评分不能为空", trigger: ["blur", "change"] },
          { pattern: /^(?:0|[1-9][0-9]?|100)$/ , message: '分值必须在0-100之间', trigger: ["blur", "change"],}
        ],
      },
      dialogType: "",
      showColumn: false,
      gdTypeStatistics: [],
      dailyStatistics: [],
      malfunctionStatistics: [],
      overtimeStatistics: [],
      quantityStatistics: {},
      headerStatistics: {},
    }
  },
  created() {
    this.getList()
    this.getHeaderStatistics()
  },
  methods: {
    /** 查询角色列表 */
    getList() {
      this.loading = true
      let params = {
        ...this.queryParams
      }
      if(this.dateRange.length) {
        params.evaluateStartTime = this.dateRange[0]
        params.evaluateEndTime = this.dateRange[1]
      }
      evaluateList(params).then(response => {
        if(this.queryParams.isEvaluate === "1") {
          this.showColumn = true
        }
        this.tableData = response.data.list
        this.total = response.data.total
        this.loading = false
      })
    },

    getHeaderStatistics() {
      let params = {
        evaluateType: this.queryParams.evaluateType,
      }
      headerStatistics(params).then(response => {
        this.headerStatistics = response.data
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.$refs["evaluateFrom"].resetFields()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.$refs["queryForm"].resetFields()
      this.handleQuery()
    },
    handleEvaluate(item) {
      this.time = new Date().getTime()
      let that = this
      this.$nextTick(() => {
        Object.assign(that.evaluateFrom, item)
      })
      this.getDetail(item.evaluateType, item.evaluateCycle)
      this.dialogType = 'edit'
    },
    handleCheck(item) {
      this.time = new Date().getTime()
      let that = this
      this.$nextTick(() => {
        Object.assign(that.evaluateFrom, item)
      })
      this.getDetail(item.evaluateType, item.evaluateCycle)
      this.dialogType = 'check'
    },
    getDetail(evaluateType, evaluateCycle) {
      let params = {
        evaluateType: evaluateType,
        evaluateCycle: evaluateCycle
      }
      detailStatistics(params).then(response => {
        this.gdTypeStatistics = response.data.gdTypeStatistics
        this.dailyStatistics = response.data.dailyStatistics
        this.malfunctionStatistics = response.data.malfunctionStatistics
        this.overtimeStatistics = response.data.overtimeStatistics
        this.quantityStatistics = response.data.quantityStatistics
        this.open = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["evaluateFrom"].validate(valid => {
        if (valid) {
          this.toEvaluateEdit()
        }
      })
    },
    toEvaluateEdit: function () {
      const {id, evaluateCycle, evaluateType, evaluateApplication, belongingTeam, evaluateContent, overallScore, problemEffectScore, technicalAbilityScore, operateComplianceScore, coordinationScore} = this.evaluateFrom
      let params = {id, evaluateCycle, evaluateType, evaluateApplication, belongingTeam, evaluateContent, overallScore, problemEffectScore, technicalAbilityScore, operateComplianceScore, coordinationScore}
      evaluateEdit(params).then(response => {
        this.$modal.msgSuccess("考评成功")
        this.open = false
        this.getList()
        this.reset()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.evaluate-box {
  padding: 20px;
  background: #fff;
  border-radius: 15px;
  justify-content: space-between;
  gap: 20px;
  margin: 0 20px;
  margin-bottom: 12px;
}
.evaluate-list {
  border-radius: 15px;
  padding: 25px 30px;
  position: relative;
}
.evaluate-cycle {
  background: linear-gradient( 180deg, #F5FEF2 0%, #E6FEEE 100%);
}
.evaluate-cycle:after {
  content: "";
  display: block;
  background-image: url("~@/assets/images/evaluateCenter/<EMAIL>");
  background-repeat: no-repeat;
  background-position: right;
  background-size: 100%;
  width: 80px;
  height: 80px;
  position: absolute;
  right: 30px;
  top: 20px;
}
.evaluate-object {
  background: linear-gradient( 180deg, #F6F7FF 0%, #ECECFF 100%);
}
.evaluate-object:after {
  content: "";
  display: block;
  background-image: url("~@/assets/images/evaluateCenter/<EMAIL>");
  background-repeat: no-repeat;
  background-position: right;
  background-size: 100%;
  width: 80px;
  height: 80px;
  position: absolute;
  right: 30px;
  top: 20px;
}
.evaluate-number {
  background: linear-gradient( 180deg, #FFF8F1 0%, #FFEFE7 100%);
}
.evaluate-number:after {
  content: "";
  display: block;
  background-image: url("~@/assets/images/evaluateCenter/<EMAIL>");
  background-repeat: no-repeat;
  background-position: right;
  background-size: 100%;
  width: 80px;
  height: 80px;
  position: absolute;
  right: 30px;
  top: 20px;
}
.evaluate-list .content {
  margin-top: 14px;
  font-size: 24px;
  color: #1D2129;
  font-weight: 500;
}
::v-deep .evaluate-dialog-wrapper {
  margin-bottom: 6vh;
}
::v-deep .evaluate-dialog-wrapper .el-dialog__header{
  display: none;
}
::v-deep .evaluate-dialog-wrapper .el-dialog__body{
  padding: 0;
  padding-bottom: 20px;
}
.evaluate-dialog-header {
  background: url("~@/assets/images/evaluateCenter/<EMAIL>") no-repeat center;
  height: 63px;
  line-height: 63px;
  padding: 0 20px;
}
.evaluate-dialog-title {
  font-weight: 400;
  font-size: 28px;
  color: #FFFFFF;
}
.evaluate-dialog-subtitle {
  font-weight: 400;
  font-size: 14px;
  color: #5688FF;
  height: 30px;
  line-height: 30px;
  background-color: #fff;
  background-image: url("~@/assets/images/evaluateCenter/<EMAIL>");
  background-repeat: no-repeat;
  background-position: 6px center;
  background-size: 14px;
  margin-top: 15px;
  margin-left: 12px;
  padding-left: 26px;
  padding-right: 6px;
}
.evaluate-dialog-chart {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  padding-bottom: 0;
}
.evaluate-dialog-chart-title {
  font-weight: 700;
  font-size: 18px;
  color: #1D2129
}
.evaluate-dialog-chart-overview {
  gap: 20px;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
  margin-top: 10px;
}
.evaluate-dialog-chart-overview-list {
  width: calc(50% - 10px);
  padding: 15px;
  position: relative;
  border-radius: 10px;
}
.evaluate-dialog-chart-overview-list-title {
  font-weight: 400;
  font-size: 16px;
  color: #4E5969;
}
.evaluate-dialog-chart-overview-list-content {
  font-weight: 700;
  font-size: 24px;
  color: #1D2129;
  margin-top: 6px;
}
.evaluate-dialog-chart-overview-list-content span {
  font-weight: 700;
  font-size: 18px;
  color: #1D2129;
  margin-left: 5px;
}
.evaluate-dialog-chart-overview-app-number {
  background: linear-gradient( 180deg, #F5FEF2 0%, #E6FEEE 100%);
}
.evaluate-dialog-chart-overview-app-number:after {
  content: "";
  display: block;
  background: url("~@/assets/images/evaluateCenter/<EMAIL>") no-repeat center;
  background-size: 60px;
  position: absolute;
  right: 20px;
  width: 60px;
  height: 60px;
  top: 13px;
}
.evaluate-dialog-chart-overview-rate {
  background: linear-gradient( 180deg, #F2F9FE 0%, #E6F4FE 100%);
}
.evaluate-dialog-chart-overview-rate:after {
  content: "";
  display: block;
  background: url("~@/assets/images/evaluateCenter/<EMAIL>") no-repeat center;
  background-size: 60px;
  position: absolute;
  right: 20px;
  width: 60px;
  height: 60px;
  top: 13px;
}
.evaluate-dialog-chart-overview-report-number {
  background: linear-gradient( 180deg, #F6F7FF 0%, #ECECFF 100%);
}
.evaluate-dialog-chart-overview-report-number:after {
  content: "";
  display: block;
  background: url("~@/assets/images/evaluateCenter/<EMAIL>") no-repeat center;
  background-size: 60px;
  position: absolute;
  right: 20px;
  width: 60px;
  height: 60px;
  top: 13px;
}
.evaluate-dialog-chart-overview-overtime-number {
  background: linear-gradient( 180deg, #FFF8F1 0%, #FFEFE7 100%)
}
.evaluate-dialog-chart-overview-overtime-number:after {
  content: "";
  display: block;
  background: url("~@/assets/images/evaluateCenter/<EMAIL>") no-repeat center;
  background-size: 60px;
  position: absolute;
  right: 20px;
  width: 60px;
  height: 60px;
  top: 13px;
}
.chart-box {
  margin:0 20px;
  height: 260px;
  background: #F9FAFB;
  margin-top: 20px;
  padding: 15px;
}
.evaluate-dialog-form {
  padding: 20px;
  border-bottom: 1px solid #E5E6EB;
}
.form-box {
  margin-top: 10px;
}
::v-deep .demo-form-inline .el-form-item__label {
  padding-bottom: 0;
}
::v-deep .demo-form-inline .el-form-item {
  padding-bottom: 0;
  margin-bottom: 15px;
}
.evaluateContent {
  display: block;
}
.dialog-footer {
  text-align: right;
}
.icon-close {
  display: block;
  background: url("~@/assets/images/evaluateCenter/<EMAIL>") no-repeat right center;
  background-size: 14px;
  width: 14px;
  height: 60px;
  flex: 1;
  cursor: pointer;
}
</style>
