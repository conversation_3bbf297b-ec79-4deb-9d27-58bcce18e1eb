<template>
  <div class="container">
    <div class="timeSelector flex-c">
      <div>统计日期</div>
      <el-date-picker
        v-model="dateArr"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        size="mini"
        @change="initData"
      >
      </el-date-picker>
    </div>
    <div class="card">
      <gjzl :list="topList"></gjzl>
    </div>
    <div class="flex-s">
      <div class="left">
        <div class="card">
          <div class="cardTitle">安全告警数量趋势</div>
          <aqgj :data="aqgjData"></aqgj>
        </div>
        <div class="card">
          <div class="cardTitle">应用告警数量趋势</div>
          <yygj :data="yygjData"></yygj>
        </div>
        <div class="card">
          <div class="cardTitle">云资源告警数量趋势</div>
          <yzygj :data="yzygjData"></yzygj>
        </div>
      </div>
      <div class="center">
        <div class="card">
          <div class="cardTitle">告警紧急程度分析</div>
          <gjjjcd :data="gjjjcdData"></gjjjcd>
        </div>
        <div class="card">
          <div class="cardTitle">高频安全隐患类型TOP5</div>
          <gpsj :data="gpsjData"></gpsj>
        </div>
      </div>
      <div class="right">
        <div class="card">
          <div class="cardTitle">告警单位排名</div>
          <gjdw :data="gjdwData"></gjdw>
        </div>
        <div class="card">
          <div class="cardTitle">告警处置时长</div>
          <gjcz :data="gjczData"></gjcz>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import gjzl from "@/views/warning/warningOverview/components/gjzl.vue";
import aqgj from "@/views/warning/warningOverview/components/aqgj.vue";
import yygj from "@/views/warning/warningOverview/components/yygj.vue";
import yzygj from "@/views/warning/warningOverview/components/yzygj.vue";
import gjjjcd from "@/views/warning/warningOverview/components/gjjjcd.vue";
import gpsj from "@/views/warning/warningOverview/components/gpsj.vue";
import gjdw from "@/views/warning/warningOverview/components/gjdw.vue";
import gjcz from "@/views/warning/warningOverview/components/gjcz.vue";
import {
  getGjs,
  getDczgj,
  getAqgjslqs,
  getYygjqs,
  getYzygjslqs,
  getGjjjcdfx,
  getGpsjlx,
} from "@/api/warning/index";
export default {
  components: { gjzl, aqgj, yygj, yzygj, gjjjcd, gpsj, gjdw, gjcz },
  data() {
    return {
      dateArr: [],
      //告警总数
      topList: [
        {
          name: "告警总数",
          value: "0",
          unit: "个",
          icon: require("@/assets/images/warning/csgj.png"),
          list: [
            { label: "应用监测告警", value: "0", unit: "个" },
            { label: "安全隐患告警", value: "0", unit: "个" },
            { label: "云资源告警", value: "0", unit: "个" },
            { label: "数据库告警", value: "0", unit: "个" },
          ],
        },
        {
          name: "待处置告警",
          value: "0",
          unit: "个",
          icon: require("@/assets/images/warning/fxyh.png"),
          list: [
            { label: "应用监测告警", value: "0", unit: "个" },
            { label: "安全隐患告警", value: "0", unit: "个" },
            { label: "云资源告警", value: "0", unit: "个" },
            { label: "数据库告警", value: "0", unit: "个" },
          ],
        },
        {
          name: "未转工单告警",
          value: "0",
          unit: "个",
          icon: require("@/assets/images/warning/aqsj.png"),
          list: [
            { label: "忽略告警", value: "0", unit: "个" },
            { label: "待分配告警", value: "0", unit: "个" },
          ],
        },
      ],
      //安全告警数量趋势
      aqgjData: [],
      //应用告警数量趋势
      yygjData: [],
      //云资源告警数量趋势
      yzygjData: [],
      //告警紧急程度分析
      gjjjcdData: [],
      //高频安全隐患类型top5
      gpsjData: [],
      gjdwData: [
        { name: "中共兰溪市委党校", value: 38 },
        { name: "金华经开区国资监", value: 34 },
        { name: "兰溪市柏社乡人民", value: 30 },
        { name: "浦江县档案馆", value: 28 },
        { name: "兰溪市香溪镇人民", value: 20 },
      ],
      gjczData: [
        { name: "2小时以内", value: 43 },
        { name: "2-6小时", value: 8 },
        { name: "6-12小时", value: 24 },
        { name: "12小时以上", value: 21 },
      ],
    };
  },
  mounted() {
    this.setDefaultDateRange();
    this.initData();
  },
  methods: {
    // 设置默认时间区间：今天往前一个月
    setDefaultDateRange() {
      const today = new Date();
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(today.getMonth() - 1);

      // 格式化日期为 yyyy-MM-dd 格式
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      };

      this.dateArr = [formatDate(oneMonthAgo), formatDate(today)];
    },
    initData() {
      let params = {};
      if (this.dateRange && this.dateRange.length > 0) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      } else {
        params.startTime = null;
        params.endTime = null;
      }

      //告警总数
      getGjs(params).then((res) => {
        //告警总数
        this.topList[0].value = res.data.zs;
        this.topList[0].list[0].value = res.data.yd + res.data.jc;
        this.topList[0].list[1].value = res.data.ah;
        this.topList[0].list[2].value = res.data.dx;
        this.topList[0].list[3].value = res.data.mc;

        //未转工单告警
        this.topList[2].value = res.data.zsdfpgd;
        this.topList[2].list[0].value = res.data.zshlgd;
        this.topList[2].list[1].value = res.data.zshlgd + res.data.zsdfpgd;
      });

      //待处置告警
      getDczgj(params).then((res) => {
        //待处置告警
        this.topList[1].value = res.data.dczzs;
        this.topList[1].list[0].value = res.data.jcdcz + res.data.yddcz;
        this.topList[1].list[1].value = res.data.ahdcz;
        this.topList[1].list[2].value = res.data.dxdcz;
        this.topList[1].list[3].value = res.data.mcdcz;
      });

      //安全告警数量趋势
      getAqgjslqs(params).then((res) => {
        this.aqgjData = res.data;
      });

      //应用告警数量趋势
      getYygjqs(params).then((res) => {
        this.yygjData = res.data;
      });

      //云资源告警数量趋势
      getYzygjslqs(params).then((res) => {
        this.yzygjData = res.data;
      });

      //告警紧急程度分析
      getGjjjcdfx(params).then((res) => {
        this.gjjjcdData = res.data;
      });

      //高频安全隐患类型top5
      getGpsjlx(params).then((res) => {
        this.gpsjData = res.data;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
  position: relative;
}
.card {
  width: 100%;
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
}
.left,
.center,
.right {
  flex: 1;
  height: auto;
}
.left,
.center {
  margin-right: 12px;
}
.timeSelector {
  position: absolute;
  right: 20px;
  top: -40px;
  justify-content: flex-end;
  & > div {
    margin-right: 12px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;
    text-align: left;
  }
}
.flex-s {
  display: flex;
  align-items: stretch;
}
</style>
