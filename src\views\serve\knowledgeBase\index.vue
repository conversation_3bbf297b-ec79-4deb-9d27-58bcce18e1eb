<template>
    <div class="container knowledge_base">
        <div class="card" v-loading="rightLoading">
            <div class="title_area">
                <div class="title_left">运维知识库</div>
                <div class="title_right">
                    <el-button icon="el-icon-plus" size="small" class="mr10" @click="openDialog(0)">
                        新增知识
                    </el-button>
                    <el-input v-model="searchValue" placeholder="搜索知识文档" suffix-icon="el-icon-search"
                        clearable></el-input>
                </div>
            </div>
            <splitpanes :horizontal="this.$store.getters.device === 'mobile'" class="default-theme">
                <pane size="16">
                    <div class="left_menu_area">
                        <left-com :menuData="menuData" @item-click="handleMenuClick" :active-item-id="currentActiveId"
                            :filter-text="searchValue">

                        </left-com>
                    </div>

                </pane>
                <pane size="84">
                    <div class="right_content_area">
                        <template v-if="showAddForm.title">
                            <div class="content_area_title">
                                <span class="mr10">{{ showAddForm.title }}</span>
                                <el-button size="small" type="primary" @click="openDialog(1)">
                                    编辑文档
                                </el-button>
                            </div>
                            <div v-html="showAddForm.describe">
                            </div>
                            <el-image style="max-width: 300px;" :src="showAddForm.filePath"
                                :preview-src-list="[showAddForm.filePath]"
                                v-if="getFileType(showAddForm.filePath) === 'img'">
                            </el-image>
                            <div class="is_other_type" @click="openFile(showAddForm.filePath)" v-else>
                                <img :src="getFileIcon(getFileTypeByUrl(showAddForm.filePath))" :alt="showAddForm.fileName" />
                                <span>{{ showAddForm.fileName }}</span>
                            </div>
                        </template>
                        <template v-else>
                            <el-empty :image-size="200"></el-empty>
                        </template>
                    </div>
                </pane>
            </splitpanes>

        </div>

        <el-dialog :title="dialogTitle" :visible.sync="open" width="780px" append-to-body
            custom-class="knowledge_base-dialog">
            <el-form ref="addForm" :model="addForm" :rules="rules" label-width="80px" label-position="top">
                <el-row :gutter="20">
                    <template v-if="lookFlag">
                        <el-col :span="12">
                            <el-form-item label="新增人" prop="createUserName">
                                <el-input v-model="addForm.createUserName" maxlength="20" placeholder="新增人" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系电话" prop="createUserPhone">
                                <el-input v-model="addForm.createUserPhone" placeholder="联系电话" />
                            </el-form-item>
                        </el-col>
                    </template>
                    <el-col :span="12">
                        <el-form-item label="知识分类" prop="typeList">
                            <el-cascader v-model="addForm.typeList" :options="processedMenuData" clearable class="w100p"
                                :props="cascaderProps" placeholder="请选择知识分类"></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="标题" prop="title">
                            <el-input v-model="addForm.title" placeholder="请输入标题" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="描述" prop="describe">
                            <editor :readOnly="!!lookFlag" v-model="addForm.describe" :min-height="192"
                                :key="editorKey" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="附件">
                            <FileUpload :limit="1" v-model="addForm.fjList"></FileUpload>
                        </el-form-item>

                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="open = false">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { Splitpanes, Pane } from "splitpanes"
import "splitpanes/dist/splitpanes.css"
import LeftCom from "./components/leftMenu.vue"
import { getClassifyData, editClassifyData, getClassifyDataDetail } from "@/api/serve/knowledgeBase.js";
import FileUpload from "@/components/FileUpload/index.vue";

export default {
    components: { Splitpanes, Pane, LeftCom },
    data() {
        return {
            currentActiveId: '',
            searchValue: '',
            selectedMenu: null,
            cascaderProps: {
                value: 'treeId',
                label: 'treeName',
                children: 'children',
            },
            menuData: [],
            lookFlag: false,
            addForm: {
                typeList: [],
                title: '',
                describe: '',
                fjList: '',
            },
            rules: {
                typeId: [
                    { required: true, message: "知识分类不能为空", trigger: "change" },
                ],
                title: [
                    { required: true, message: "标题不能为空", trigger: "blur" },
                ],
                describe: [
                    { required: true, message: "描述不能为空", trigger: "blur" },
                ]
            },
            dialogTitle: '',
            open: false,
            editorKey: 1,
            showAddForm: {},
            rightLoading: false,
        }
    },
    computed: {
        processedMenuData() {
            return this.menuData.map(level1 => {
                const level1Clone = { ...level1 };
                if (level1Clone.children) {
                    level1Clone.children = level1Clone.children.map(level2 => {
                        const { children, ...level2WithoutChildren } = level2;
                        return level2WithoutChildren;
                    });
                }
                return level1Clone;
            });
        }
    },
    methods: {
        handleMenuClick(item) {
            this.selectedMenu = item;
            this.currentActiveId = item.treeId;
            let id = item.treeId.match(/(?<=library:)[^:]+$/)?.at(0)
            this.rightLoading = true
            getClassifyDataDetail(id).then(res => {
                this.rightLoading = false
                if (res.code === 200) {
                    this.showAddForm = { ...res.data }
                }
            })
        },
        submitForm() {
            this.$refs.addForm.validate((valid) => {
                if (valid) {
                    let query = { ...this.addForm }
                    query.id = this.addForm.id || 0
                    query.filePath = this.addForm.fjList || ''
                    query.fileName = query.filePath ? query.filePath.match(/\/([^\/]+)$/)[1] : ''
                    query.typeList = query.typeList.join(',')
                    query.typeId = this.getLastTypeValue(this.addForm.typeList)
                    editClassifyData(query).then(res => {
                        if (res.code === 200) {
                            this.open = false
                            this.$modal.msgSuccess(this.addForm.id ? "修改成功" : "新增成功");
                            this.getClassifyDataFn()
                            this.showAddForm = {}
                        }
                    })
                }
            })

        },
        getLastTypeValue(arr) {
            if (!Array.isArray(arr) || arr.length === 0) return '';
            const lastItem = arr[arr.length - 1];
            return lastItem.match(/(?<=type:)[^:]+$/)?.at(0) || '';
        },
        openDialog(val) {
            this.dialogTitle = val ? '修改知识库' : '新增知识库'
            this.addForm = val ? {} : this.$options.data().addForm
            this.editorKey++
            this.open = true
            if (val) {
                this.addForm = { ...this.showAddForm }
                this.addForm.fjList = this.showAddForm.filePath
                this.addForm.typeList = this.showAddForm.typeList.split(',')
            }
        },
        getClassifyDataFn() {
            getClassifyData().then(res => {
                if (res.code === 200) {
                    this.menuData = res.data || []
                }
            })
        },
        getFileType(filePath) {
            const extension = this.getExtensionFromPath(filePath);
            const imageExtensions = new Set([
                'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'ico'
            ]);
            return imageExtensions.has(extension) ? 'img' : 'other';
        },
        getExtensionFromPath(path) {
            if (typeof path !== 'string' || path.trim() === '') return '';
            const filenameMatch = path.match(/[^/?#]+$/);
            if (!filenameMatch) return '';
            const filename = filenameMatch[0];
            const lastDotIndex = filename.lastIndexOf('.');
            if (lastDotIndex === -1 || lastDotIndex === 0 || lastDotIndex === filename.length - 1) {
                return '';
            }
            return filename.slice(lastDotIndex + 1).toLowerCase();
        },

        getFileIcon(type) {
            const iconMap = {
                1: require("@/assets/images/serve/word.png"), // 文档
                2: require("@/assets/images/serve/excel.png"), // 表格
                3: require("@/assets/images/serve/pdf.png"), // PDF
                4: "", // 图片类型不使用图标，直接显示缩略图
            };

            return iconMap[type] || require("@/assets/images/serve/word.png");
        },
        openFile(url) {
            window.open(url)
        },
        getFileTypeByUrl(url) {
            const extension = this.getExtensionFromUrl(url);
            const typeMap = {
                'doc': 1,
                'docx': 1,
                'xls': 2,
                'xlsx': 2,
                'xlsm': 2,
                'xlsb': 2,
                'pdf': 3
            };
            return typeMap[extension] || 0;
        },
        getExtensionFromUrl(url) {
            if (typeof url !== 'string' || url.trim() === '') return '';
            const filenameMatch = url.match(/[^/?#]+$/);
            if (!filenameMatch) return '';
            const filename = filenameMatch[0];
            const lastDotIndex = filename.lastIndexOf('.');
            if (lastDotIndex === -1 || lastDotIndex === 0 || lastDotIndex === filename.length - 1) {
                return '';
            }
            return filename.slice(lastDotIndex + 1).toLowerCase();
        }
    },
    mounted() {
        this.getClassifyDataFn()
    }
}

</script>

<style lang="scss" scoped>
.card {
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
}

.title_area {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #EAECF0;
    padding-bottom: 20px;
}

.title_left {
    font-size: 22px;
    color: #1D2129;
    font-weight: 700;
}

.title_right {
    display: flex;
}

.left_menu_area {
    padding: 20px 10px 20px 0;
}

.right_content_area {
    padding: 30px;
}

.content_area_title {
    font-size: 18px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
}

.w100p {
    width: 100%;
}

.is_other_type {
    display: flex;
    align-items: center;
    padding: 10px;
    cursor: pointer;
    width: fit-content;
    transition: all .3s;

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        background-color: #f8f9fa;
    }
}
</style>

<style lang="scss">
// #app .knowledge_base .el-submenu .el-submenu__title {
//     padding-left: 20px !important;
// }
.knowledge_base-dialog {
    border-radius: 10px;

    .el-dialog__header {
        background: #F2F2F2;
        border-radius: 10px 10px 0 0;
    }

    .el-dialog__footer {
        border-top: 1px solid #E5E6EB;
        border-radius: 0 0 10px 10px;
    }
}
</style>