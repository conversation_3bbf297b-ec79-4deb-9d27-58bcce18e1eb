import request from "@/utils/request";

// 告警总数
export function getGjs(query) {
  return request({
    url: "/gjzl/gjs",
    method: "get",
    params: query,
  });
}

// 待处置告警
export function getDczgj(query) {
  return request({
    url: "/gjzl/dczgj",
    method: "get",
    params: query,
  });
}

// 安全告警数量趋势
export function getAqgjslqs(query) {
  return request({
    url: "/gjzl/aqgjslqs",
    method: "get",
    params: query,
  });
}

// 应用告警趋势
export function getYygjqs(query) {
  return request({
    url: "/gjzl/yygjqs",
    method: "get",
    params: query,
  });
}

// 云资源告警数量趋势
export function getYzygjslqs(query) {
  return request({
    url: "/gjzl/yzygjslqs",
    method: "get",
    params: query,
  });
}

// 告警紧急程度分析
export function getGjjjcdfx(query) {
  return request({
    url: "/gjzl/gjjjcdfx",
    method: "get",
    params: query,
  });
}

// 高频安全隐患类型top5
export function getGpsjlx(query) {
  return request({
    url: "/gjzl/gpsjlx",
    method: "get",
    params: query,
  });
}