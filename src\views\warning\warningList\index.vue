<template>
  <div class="container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item prop="yyId">
        <el-select
          v-model="queryParams.yyId"
          placeholder="所属应用"
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="(item, i) in appOptions"
            :key="i"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="deptId">
        <treeselect
          style="width: 160px; height: 32px"
          v-model="queryParams.deptId"
          :options="enabledDeptOptions"
          :show-count="true"
          placeholder="请选择归属部门"
        />
      </el-form-item>
      <el-form-item prop="level">
        <el-select v-model="queryParams.level" placeholder="告警等级" clearable>
          <el-option
            v-for="dict in levelOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="dateArr">
        <el-date-picker
          v-model="queryParams.dateArr"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
    <div class="card">
      <div class="cardTitle">应用监测告警</div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="应用运行告警" name="first"></el-tab-pane>
        <el-tab-pane label="基础设施告警" name="second"></el-tab-pane>
      </el-tabs>
      <yyjk
        v-show="activeName == 'first'"
        :yyId="queryParams.yyId"
        :deptId="queryParams.deptId"
        :dateArr="queryParams.dateArr"
        :level="queryParams.level"
        :activeName="activeName"
      ></yyjk>
      <qtjk
        v-show="activeName == 'second'"
        :yyId="queryParams.yyId"
        :deptId="queryParams.deptId"
        :dateArr="queryParams.dateArr"
        :level="queryParams.level"
        :activeName="activeName"
      ></qtjk>
    </div>
    <div class="card">
      <div class="cardTitle">安全隐患告警</div>
      <aqyh
        :yyId="queryParams.yyId"
        :deptId="queryParams.deptId"
        :dateArr="queryParams.dateArr"
        :level="queryParams.level"
      ></aqyh>
    </div>
    <div class="card">
      <div class="cardTitle">云资源告警</div>
      <yzy
        :yyId="queryParams.yyId"
        :deptId="queryParams.deptId"
        :dateArr="queryParams.dateArr"
        :level="queryParams.level"
      ></yzy>
    </div>
    <div class="card">
      <div class="cardTitle">数据库告警</div>
      <sjk
        :yyId="queryParams.yyId"
        :deptId="queryParams.deptId"
        :dateArr="queryParams.dateArr"
        :level="queryParams.level"
      ></sjk>
    </div>
  </div>
</template>

<script>
import yyjk from "@/views/warning/warningList/components/yyjk.vue";
import yzy from "@/views/warning/warningList/components/yzy.vue";
import aqyh from "@/views/warning/warningList/components/aqyh.vue";
import sjk from "@/views/warning/warningList/components/sjk.vue";
import qtjk from "@/views/warning/warningList/components/qtjk.vue";
import { listAllYy, deptTreeSelect } from "@/api/serve/orderlist";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  components: { yyjk, yzy, aqyh, sjk, qtjk, Treeselect },
  data() {
    return {
      activeName: "first",
      queryParams: {
        yyId: undefined,
        deptId: undefined,
        level: undefined,
        dateArr: [],
      },
      appOptions: [],
      enabledDeptOptions: [],
      levelOptions: [
        { label: "特别紧急", value: '1' },
        { label: "紧急", value: '2' },
        { label: "重要", value: '3' },
        { label: "一般", value: '4' },
      ],
    };
  },
  mounted() {
    this.initData();
    if (this.$route.query.levelType) {
      this.queryParams.level = this.$route.query.levelType;
    } else {
      // 设置默认时间区间：今天往前一个月
      this.setDefaultDateRange();
    }
  },
  methods: {
    // 设置默认时间区间：今天往前一个月
    setDefaultDateRange() {
      const today = new Date();
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(today.getMonth() - 1);

      // 格式化日期为 yyyy-MM-dd 格式
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      };

      this.queryParams.dateArr = [formatDate(oneMonthAgo), formatDate(today)];
    },
    initData() {
      listAllYy().then((res) => {
        this.appOptions = res.data;
      });
      /** 查询部门下拉树结构 */
      deptTreeSelect().then((response) => {
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 40px;
}
.card {
  border-radius: 15px;
  padding: 20px 20px;
  box-sizing: border-box;
  background-color: #fff;
  height: auto;
  margin-bottom: 12px;
  .cardTitle {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 18px;
    color: #1d2129;
    line-height: 24px;
    text-align: left;
    margin-right: 30px;
    margin-bottom: 10px;
  }
}
::v-deep .el-table__cell {
  padding: 6px 0;
}
::v-deep .vue-treeselect__control {
  height: 32px;
  box-sizing: border-box;
}
</style>
