<template>
  <div style="width: 100%; height: 223px" id="gpsjlx"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {},
  watch: {
    data: {
      handler(val) {
        this.initBar<PERSON>hart(val.reverse(), "gpsjlx");
      },
      deep: true,
    },
  },
  methods: {
    initBarChart(data, id) {
      const barChart = echarts.init(document.getElementById(id));
      const option = {
        color: ["#98DC3E", "#22E197", "#00EAFF", "#007BFF"],
        grid: {
          left: 0,
          right: 0,
          top: 10,
          bottom: 0,
        },
        tooltip: {
          trigger: "item",
        },
        xAxis: {
          type: "value",
          splitLine: { show: false },
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
        },
        yAxis: {
          type: "category",
          data: data.map((x) => x.name),
          axisLabel: {
            show: true,
            inside: true,
            splitNumber: 50,
            boundaryGap: [20, 20],
            textStyle: {
              color: "#1D252F",
              verticalAlign: "bottom",
              align: "left",
              fontSize: 14,
              padding: [200, 0, 12, -3],
            },
          },
          axisTick: { show: false },
          axisLine: { show: false },
        },
        series: [
          {
            name: "",
            type: "bar",
            barWidth: 7,
            itemStyle: {
              color: "#21CCFF",
              borderRadius: 5,
            },
            label: { show: false },
            data: data.map((v) => v.num),
          },
          {
            name: "",
            type: "bar",
            barGap: "-100%",
            barWidth: 7,
            data: data.map(() => Math.max(...data.map((x) => x.num))),
            label: {
              show: true,
              position: "right",
              align: "center",
              offset: [-20, -20],
              fontSize: 14,
              color: "#1D252F",
              // formatter: (params) => {
              //   return data[params.dataIndex].value;
              // },
            },
            itemStyle: {
              color: "#F2F3F8",
              borderRadius: 5,
            },
            tooltip: { show: false },
            z: 0,
            silent: true,
          },
        ],
      };

      barChart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
