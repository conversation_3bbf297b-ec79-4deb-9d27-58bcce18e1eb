<template>
  <div class="navbar">
    <!-- <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    /> -->
    <!-- <div class="title">统一运维平台</div> -->
    <img src="@/assets/images/logo.png" class="logo" />
    <!-- <breadcrumb
      v-if="!topNav"
      id="breadcrumb-container"
      class="breadcrumb-container"
    /> -->
    <div class="content">
      <top-nav
        v-if="topNav"
        id="topmenu-container"
        class="topmenu-container"
        :grayed-menu-names="grayedMenuNames"
      />

      <div class="right-menu">
        <template v-if="device !== 'mobile'">
          <el-badge :value="12" class="msg_box">
            <!-- <i class="el-icon-message-solid"></i> -->
            <img src="@/assets/images/setting/ring.png" class="msgIcon" />
          </el-badge>
          <!-- <el-badge class="msg_box">
            <img src="@/assets/images/setting/setting.png" class="msgIcon" />
          </el-badge> -->
          <!-- <search id="header-search" class="right-menu-item" /> -->

          <!-- <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip>

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
        </template>

        <el-dropdown
          class="avatar-container right-menu-item hover-effect"
          trigger="hover"
        >
          <div class="avatar-wrapper">
            <img :src="avatar" class="user-avatar" />
            <!-- <span class="user-nickname"> {{ nickName }} </span> -->
          </div>
          <el-dropdown-menu slot="dropdown">
            <router-link to="/user/profile">
              <el-dropdown-item>个人中心</el-dropdown-item>
            </router-link>
            <el-dropdown-item divided @click.native="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <!-- <div class="right-menu-item hover-effect setting" @click="setLayout" v-if="setting">
        <svg-icon icon-class="more-up" />
      </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";

export default {
  emits: ["setLayout"],
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc,
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device", "nickName"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
    // 置灰菜单配置，可以根据用户权限、系统状态等动态配置
    grayedMenuNames() {
      // 示例：根据用户角色或系统状态动态配置置灰菜单
      const userRoles = this.$store.getters.roles || [];
      const grayedMenus = [];

      // 示例逻辑：如果用户不是管理员，置灰系统管理
      if (!userRoles.includes("admin")) {
        grayedMenus.push("系统管理");
      }

      // 示例逻辑：根据系统维护状态置灰某些菜单
      // if (this.$store.state.system.maintenance) {
      //   grayedMenus.push('监控中心', '工具管理');
      // }

      // 可以从后端API获取置灰菜单列表
      // 或者从本地配置文件读取
      return grayedMenus.concat([
        "日志查询",
        "值班值守",
        "评价总览",
        "资产总览",
      ]); // 示例：默认置灰监控菜单
    },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    setLayout(event) {
      this.$emit("setLayout");
    },
    logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/home";
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.logo {
  width: 155px;
  height: 28px;
  margin: 0 30px;
}
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  // box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  box-shadow: 0px 4px 10px 0px rgba(78, 89, 105, 0.06);
  z-index: 1;
  display: flex;
  // justify-content: space-between;
  align-content: center;
  align-items: center;
  .title {
    margin-left: 24px;
    font-size: 30px;
  }
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-content: center;
    align-items: center;
    margin-left: 108px;
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    // float: left;
  }

  .topmenu-container {
    // position: absolute;
    // left: 50px;
    margin-right: 24px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    // float: right;
    height: 100%;
    line-height: 50px;
    margin-right: 24px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 0px;
      padding-right: 0px;

      .avatar-wrapper {
        margin-top: 14px;
        position: relative;
        display: flex;
        align-content: center;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          width: 32px;
          height: 32px;
          border-radius: 50%;
        }

        .user-nickname {
          // position: relative;
          // bottom: 10px;
          font-size: 14px;
          font-weight: bold;
          margin-left: 8px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
.el-icon-message-solid {
  font-size: 32px;
}
.msg_box {
  margin-right: 48px;
  // margin-bottom: 10px;
  height: 44px;
}
.msgIcon {
  width: 32px;
  height: 32px;
}
::v-deep .el-badge__content.is-fixed {
  top: 4px !important;
  right: 12px !important;
}
</style>
