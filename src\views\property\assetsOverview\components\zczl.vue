<template>
  <div class="wrap">
    <div class="itemList flex-b">
      <div class="item" v-for="(item, i) in list" :key="i">
        <div class="flex-b">
          <div>
            <div class="name">{{ item.name }}</div>
            <div class="num">
              {{ item.num }} <span class="unit">{{ item.unit }}</span>
            </div>
          </div>
          <img :src="item.icon" class="icon" />
        </div>
        <div class="list flex-b">
          <div class="liBox" v-for="(x, j) in item.list" :key="j">
            <div class="li flex-c">
              <div class="label">{{ x.label }}:{{ x.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="scss" scoped>
.itemList {
  align-items: stretch;
}
.item {
  width: calc((100% - 16 * 3px) / 4);
  margin-right: 16px;
  padding: 12px 16px;
  box-sizing: border-box;
  background: linear-gradient(180deg, #f2f9fe 0%, #e6f4fe 100%);
  border-radius: 10px 10px 10px 10px;
  &:last-child {
    margin-right: 0;
  }
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 16px;
    color: #4e5969;
    line-height: 28px;
    text-align: left;
  }
  .num {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 24px;
    color: #1d2129;
    line-height: 42px;
    text-align: left;
    .unit {
      font-size: 18px;
    }
  }
  .icon {
    width: 65px;
    height: 65px;
  }
  .list {
    margin-top: 12px;
    flex-wrap: wrap;
    .liBox {
      width: 50%;
      &:nth-child(2n + 2) > .li {
        display: flex;
        justify-content: flex-end;
      }
      .li {
        width: 100%;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 16px;
        color: #4e5969;
        line-height: 28px;
        text-align: left;
      }
    }
  }
}
</style>
