<template>
  <div style="width: 100%; height: 223px" id="gjczsc"></div>
</template>

<script>
import * as echarts from "echarts";
export default {
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  mounted() {
    if (this.data) {
      this.initChart(this.data);
    }
  },
  methods: {
    initChart(data) {
      let total = 0;
      data.forEach((x) => {
        total += parseInt(x.value);
      });
      let chart = echarts.init(document.getElementById("gjczsc"));
      let option = {
        color: ["#3AA1FF", "#FFCA3A", "#FF7D00", "#FF3A3A"],
        title: {
          text: total,
          subtext: "总数",
          top: "28%",
          left: "center",
          textStyle: {
            fontSize: 24,
            lineHeight: 24,
          },
          subtextStyle: {
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          right: "12%",
          top: "bottom",
          icon: "circle",
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 18,
          textStyle: {
            padding: [0, 0, 0, 6],
          },
        },
        series: [
          {
            type: "pie",
            data: data,
            radius: ["48%", "70%"],
            center: ["50%", "40%"],
            label: {
              show: true,
              position: "outside", // 将标签放置在扇区外部
              formatter: "{d}%", // 标签内容格式化
            },
            labelLine: {
              show: true,
              length: 10, // 连接线长度
              length2: 14, // 引导线长度
            },
            data: data,
          },
        ],
      };
      chart.setOption(option);
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
